# G<PERSON>dra AI Agent Configuration
# Copy this file to .env and modify the values as needed

# Server Settings
GHIDRA_AI_HOST=127.0.0.1
GHIDRA_AI_PORT=8000
GHIDRA_AI_DEBUG=false
GHIDRA_AI_SERVE_FRONTEND=true

# AI Model Settings
GHIDRA_AI_MODEL_PATH=
GHIDRA_AI_MODEL_NAME=llama-2-7b-chat.gguf
GHIDRA_AI_MAX_TOKENS=2048
GHIDRA_AI_TEMPERATURE=0.7

# LLama.cpp Settings
GHIDRA_AI_N_CTX=4096
GHIDRA_AI_N_THREADS=4
GHIDRA_AI_N_GPU_LAYERS=0

# Ghidra Settings
GHIDRA_AI_GHIDRA_PATH=
GHIDRA_AI_GHIDRA_PROJECTS_DIR=ghidra_projects

# GhidraMCP Settings
GHIDRA_AI_MCP_HOST=localhost
GHIDRA_AI_MCP_PORT=8080
GHIDRA_AI_MCP_TIMEOUT=30
GHIDRA_AI_MCP_RETRY_ATTEMPTS=3
GHIDRA_AI_MCP_ENABLED=true

# LLama.cpp Extended Settings
GHIDRA_AI_LLAMA_MODEL_DIR=./llama-cpp/models
GHIDRA_AI_LLAMA_N_CTX=4096
GHIDRA_AI_LLAMA_N_THREADS=-1
GHIDRA_AI_LLAMA_N_GPU_LAYERS=0
GHIDRA_AI_LLAMA_USE_MMAP=true
GHIDRA_AI_LLAMA_USE_MLOCK=false
GHIDRA_AI_LLAMA_VERBOSE=false

# Frontend Settings
GHIDRA_AI_FRONTEND_BUILD_DIR=./frontend/build
GHIDRA_AI_FRONTEND_DEV_PORT=3000
GHIDRA_AI_FRONTEND_API_BASE_URL=http://localhost:8000

# Testing Settings
GHIDRA_AI_TEST_DATA_DIR=./tests/data
GHIDRA_AI_TEST_TEMP_DIR=./tests/temp
GHIDRA_AI_TEST_GHIDRA_PROJECTS_DIR=./tests/ghidra_projects

# Logging Settings
GHIDRA_AI_LOG_LEVEL=INFO
GHIDRA_AI_LOG_FILE=
GHIDRA_AI_LOG_STRUCTURED=false
GHIDRA_AI_LOG_MAX_BYTES=10485760
GHIDRA_AI_LOG_BACKUP_COUNT=5

# Security Settings
GHIDRA_AI_API_KEY=
GHIDRA_AI_CORS_ORIGINS=http://localhost:3000
GHIDRA_AI_MAX_UPLOAD_SIZE=104857600
GHIDRA_AI_RATE_LIMIT_REQUESTS=100
GHIDRA_AI_RATE_LIMIT_WINDOW=3600

# Performance Settings
GHIDRA_AI_WORKER_PROCESSES=1
GHIDRA_AI_MAX_CONCURRENT_ANALYSES=3
GHIDRA_AI_ANALYSIS_TIMEOUT=1800
GHIDRA_AI_CLEANUP_INTERVAL=3600
