# <PERSON>hidra AI Agent Configuration
# Copy this file to .env and modify the values as needed

# Server Settings
GHIDRA_AI_HOST=127.0.0.1
GHIDRA_AI_PORT=8000
GHIDRA_AI_DEBUG=false
GHIDRA_AI_SERVE_FRONTEND=true

# CORS Settings (comma-separated list)
GHIDRA_AI_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# AI Model Settings
GHIDRA_AI_MODEL_PATH=
GHIDRA_AI_MODEL_NAME=llama-2-7b-chat.gguf
GHIDRA_AI_MAX_TOKENS=2048
GHIDRA_AI_TEMPERATURE=0.7

# LLama.cpp Settings
GHIDRA_AI_N_CTX=4096
GHIDRA_AI_N_THREADS=4
GHIDRA_AI_N_GPU_LAYERS=0

# Ghidra Settings
GHIDRA_AI_GHIDRA_PATH=
GHIDRA_AI_GHIDRA_MCP_HOST=127.0.0.1
GHIDRA_AI_GHIDRA_MCP_PORT=3001
GHIDRA_AI_GHIDRA_PROJECTS_DIR=ghidra_projects

# MCP Settings
GHIDRA_AI_MCP_SERVER_TIMEOUT=30

# Logging
GHIDRA_AI_LOG_LEVEL=INFO
GHIDRA_AI_LOG_FILE=

# Security
GHIDRA_AI_API_KEY=
