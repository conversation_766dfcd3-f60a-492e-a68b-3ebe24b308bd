"""
Prompt Manager for AI interactions in Ghidra reverse engineering tasks.

This module provides the PromptManager class that handles AI prompt generation
and formatting for different types of reverse engineering analysis.
"""

import logging
from typing import Dict, Any, Optional, List
import json

logger = logging.getLogger(__name__)


class PromptManager:
    """Manager for AI prompt generation and formatting."""

    def __init__(self):
        """Initialize the prompt manager with templates."""
        self.analysis_templates = {
            "full": self._get_full_analysis_template(),
            "quick": self._get_quick_analysis_template(),
            "function": self._get_function_analysis_template(),
            "security": self._get_security_analysis_template()
        }

        self.chat_template = self._get_chat_template()

    def create_analysis_prompt(self, ghidra_data: Dict[str, Any], analysis_type: str = "full") -> str:
        """
        Create analysis prompt based on Ghidra data and analysis type.

        Args:
            ghidra_data: Data from Ghidra analysis
            analysis_type: Type of analysis (full, quick, function, security)

        Returns:
            Formatted prompt string
        """
        try:
            template = self.analysis_templates.get(analysis_type, self.analysis_templates["full"])
            formatted_data = self._format_ghidra_data(ghidra_data)

            prompt = template.format(
                ghidra_data=formatted_data,
                analysis_type=analysis_type
            )

            logger.debug(f"Created {analysis_type} analysis prompt ({len(prompt)} chars)")
            return prompt

        except Exception as e:
            logger.error(f"Failed to create analysis prompt: {e}")
            return self._get_fallback_prompt(ghidra_data)

    def create_chat_prompt(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Create chat prompt for interactive AI assistance.

        Args:
            message: User message
            context: Optional context from previous interactions

        Returns:
            Formatted chat prompt
        """
        try:
            context_str = ""
            if context:
                context_str = self._format_context(context)

            prompt = self.chat_template.format(
                context=context_str,
                user_message=message
            )

            logger.debug(f"Created chat prompt ({len(prompt)} chars)")
            return prompt

        except Exception as e:
            logger.error(f"Failed to create chat prompt: {e}")
            return f"User: {message}\n\nAssistant: I'll help you with reverse engineering analysis."

    def create_function_analysis_prompt(self, function_data: Dict[str, Any]) -> str:
        """
        Create prompt for specific function analysis.

        Args:
            function_data: Function data from Ghidra

        Returns:
            Formatted function analysis prompt
        """
        try:
            template = self.analysis_templates["function"]
            formatted_function = self._format_function_data(function_data)

            prompt = template.format(function_data=formatted_function)

            logger.debug(f"Created function analysis prompt ({len(prompt)} chars)")
            return prompt

        except Exception as e:
            logger.error(f"Failed to create function analysis prompt: {e}")
            return self._get_fallback_function_prompt(function_data)

    def _format_ghidra_data(self, data: Dict[str, Any]) -> str:
        """Format Ghidra analysis data for prompt inclusion."""
        try:
            formatted_sections = []

            # Binary information
            if "binary_info" in data:
                binary_info = data["binary_info"]
                formatted_sections.append(f"Binary: {binary_info.get('name', 'Unknown')}")
                formatted_sections.append(f"Architecture: {binary_info.get('arch', 'Unknown')}")
                formatted_sections.append(f"File Type: {binary_info.get('type', 'Unknown')}")

            # Functions summary
            if "functions" in data:
                functions = data["functions"]
                if isinstance(functions, list):
                    formatted_sections.append(f"Functions Found: {len(functions)}")
                    # Include top 10 functions
                    for i, func in enumerate(functions[:10]):
                        if isinstance(func, dict):
                            name = func.get("name", f"func_{i}")
                            addr = func.get("address", "unknown")
                            formatted_sections.append(f"  - {name} @ {addr}")

            # Imports/Exports
            if "imports" in data:
                imports = data["imports"]
                if isinstance(imports, list) and imports:
                    formatted_sections.append(f"Imports ({len(imports)}): {', '.join(imports[:10])}")

            if "exports" in data:
                exports = data["exports"]
                if isinstance(exports, list) and exports:
                    formatted_sections.append(f"Exports ({len(exports)}): {', '.join(exports[:10])}")

            # Strings
            if "strings" in data:
                strings = data["strings"]
                if isinstance(strings, list) and strings:
                    formatted_sections.append(f"Notable Strings ({len(strings)}): {', '.join(strings[:5])}")

            return "\n".join(formatted_sections) if formatted_sections else "No analysis data available"

        except Exception as e:
            logger.error(f"Failed to format Ghidra data: {e}")
            return f"Raw data: {str(data)[:500]}..."

    def _format_function_data(self, function_data: Dict[str, Any]) -> str:
        """Format function data for prompt inclusion."""
        try:
            sections = []

            if "name" in function_data:
                sections.append(f"Function: {function_data['name']}")

            if "address" in function_data:
                sections.append(f"Address: {function_data['address']}")

            if "decompiled_code" in function_data:
                code = function_data["decompiled_code"]
                sections.append(f"Decompiled Code:\n{code}")

            if "assembly" in function_data:
                asm = function_data["assembly"]
                sections.append(f"Assembly:\n{asm}")

            if "calls" in function_data:
                calls = function_data["calls"]
                if isinstance(calls, list) and calls:
                    sections.append(f"Function Calls: {', '.join(calls[:10])}")

            return "\n\n".join(sections) if sections else "No function data available"

        except Exception as e:
            logger.error(f"Failed to format function data: {e}")
            return f"Raw function data: {str(function_data)[:500]}..."

    def _format_context(self, context: Dict[str, Any]) -> str:
        """Format context data for chat prompts."""
        try:
            if not context:
                return ""

            context_parts = []

            if "previous_analysis" in context:
                context_parts.append(f"Previous Analysis: {context['previous_analysis']}")

            if "current_binary" in context:
                context_parts.append(f"Current Binary: {context['current_binary']}")

            if "focus_area" in context:
                context_parts.append(f"Focus Area: {context['focus_area']}")

            return "\n".join(context_parts) if context_parts else ""

        except Exception as e:
            logger.error(f"Failed to format context: {e}")
            return ""

    def _get_full_analysis_template(self) -> str:
        """Get template for full binary analysis."""
        return """You are an expert reverse engineer analyzing a binary with Ghidra.

<analysis>
Binary Analysis Data:
{ghidra_data}

Please provide a comprehensive analysis including:
1. Binary overview and architecture assessment
2. Key functions and their purposes
3. Security implications and potential vulnerabilities
4. Notable patterns or behaviors
5. Recommendations for further investigation

Focus on actionable insights for reverse engineering.
</analysis>"""

    def _get_quick_analysis_template(self) -> str:
        """Get template for quick binary analysis."""
        return """You are an expert reverse engineer providing a quick assessment.

<analysis>
Binary Data:
{ghidra_data}

Provide a brief analysis covering:
1. Binary type and purpose
2. Key functions of interest
3. Immediate security concerns
4. Next steps for analysis

Keep the response concise but informative.
</analysis>"""

    def _get_function_analysis_template(self) -> str:
        """Get template for function-specific analysis."""
        return """You are an expert reverse engineer analyzing a specific function.

<analysis>
Function Data:
{function_data}

Please analyze this function and provide:
1. Function purpose and behavior
2. Input/output analysis
3. Security implications
4. Code quality assessment
5. Potential vulnerabilities or concerns

Focus on understanding what this function does and its significance.
</analysis>"""

    def _get_security_analysis_template(self) -> str:
        """Get template for security-focused analysis."""
        return """You are a security researcher analyzing a binary for vulnerabilities.

<analysis>
Binary Security Analysis:
{ghidra_data}

Focus on security aspects:
1. Potential vulnerabilities (buffer overflows, injection points, etc.)
2. Security mechanisms (ASLR, DEP, stack canaries)
3. Suspicious functions or behaviors
4. Attack surface analysis
5. Mitigation recommendations

Prioritize findings by severity and exploitability.
</analysis>"""

    def _get_chat_template(self) -> str:
        """Get template for chat interactions."""
        return """You are an expert reverse engineer assistant helping with binary analysis using Ghidra.

{context}

User: {user_message}

Please provide a helpful response based on the context and user's question."""

    def _get_fallback_prompt(self, data: Dict[str, Any]) -> str:
        """Get fallback prompt when template formatting fails."""
        return f"""You are an expert reverse engineer. Please analyze the following data:

{str(data)[:1000]}

Provide insights about this binary analysis data."""

    def _get_fallback_function_prompt(self, function_data: Dict[str, Any]) -> str:
        """Get fallback prompt for function analysis when template formatting fails."""
        return f"""You are an expert reverse engineer. Please analyze this function:

{str(function_data)[:1000]}

Explain what this function does and its significance."""