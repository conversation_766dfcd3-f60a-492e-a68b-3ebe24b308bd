"""
Configuration settings for the Ghidra AI Agent.
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
from pathlib import Path
import os


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Server settings
    host: str = "127.0.0.1"
    port: int = 8000
    debug: bool = False
    serve_frontend: bool = True
    
    # CORS settings
    cors_origins: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # AI Model settings
    model_path: Optional[str] = None
    model_name: str = "llama-2-7b-chat.gguf"
    max_tokens: int = 2048
    temperature: float = 0.7
    
    # LLama.cpp settings
    n_ctx: int = 4096
    n_threads: int = 4
    n_gpu_layers: int = 0  # Set > 0 if you have GPU support
    
    # Ghidra settings
    ghidra_path: Optional[str] = None
    ghidra_mcp_host: str = "127.0.0.1"
    ghidra_mcp_port: int = 3001
    ghidra_projects_dir: str = "ghidra_projects"

    # MCP settings
    mcp_server_timeout: int = 30
    
    # Logging
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    # Security
    api_key: Optional[str] = None
    
    class Config:
        env_file = ".env"
        env_prefix = "GHIDRA_AI_"
    
    @property
    def models_dir(self) -> Path:
        """Get the models directory path."""
        return Path(__file__).parent.parent.parent / "models"
    
    @property
    def full_model_path(self) -> Optional[Path]:
        """Get the full path to the model file."""
        if self.model_path:
            return Path(self.model_path)
        
        models_dir = self.models_dir
        if models_dir.exists():
            model_file = models_dir / self.model_name
            if model_file.exists():
                return model_file
        
        return None
    
    def validate_settings(self) -> List[str]:
        """Validate settings and return list of issues."""
        issues = []

        # Check if model exists
        if not self.full_model_path or not self.full_model_path.exists():
            issues.append(f"Model file not found: {self.model_name}")

        # Check Ghidra path if provided
        if self.ghidra_path and not Path(self.ghidra_path).exists():
            issues.append(f"Ghidra path not found: {self.ghidra_path}")

        # Validate GhidraMCP configuration
        if self.ghidra_mcp_enabled:
            if not (1 <= self.ghidra_mcp_port <= 65535):
                issues.append(f"Invalid GhidraMCP port: {self.ghidra_mcp_port}")
            if self.ghidra_mcp_timeout <= 0:
                issues.append(f"GhidraMCP timeout must be positive: {self.ghidra_mcp_timeout}")
            if self.ghidra_mcp_retry_attempts < 0:
                issues.append(f"GhidraMCP retry attempts must be non-negative: {self.ghidra_mcp_retry_attempts}")

        # Validate LLama.cpp configuration
        if self.llama_cpp_n_ctx <= 0:
            issues.append(f"LLama.cpp context size must be positive: {self.llama_cpp_n_ctx}")
        if self.llama_cpp_n_gpu_layers < 0:
            issues.append(f"LLama.cpp GPU layers must be non-negative: {self.llama_cpp_n_gpu_layers}")

        # Validate frontend configuration
        if not (1 <= self.frontend_dev_server_port <= 65535):
            issues.append(f"Invalid frontend dev server port: {self.frontend_dev_server_port}")

        # Validate logging configuration
        valid_log_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        if self.log_level.upper() not in valid_log_levels:
            issues.append(f"Invalid log level: {self.log_level}. Must be one of {valid_log_levels}")
        if self.log_max_bytes <= 0:
            issues.append(f"Log max bytes must be positive: {self.log_max_bytes}")
        if self.log_backup_count < 0:
            issues.append(f"Log backup count must be non-negative: {self.log_backup_count}")

        # Validate security configuration
        if self.max_upload_size <= 0:
            issues.append(f"Max upload size must be positive: {self.max_upload_size}")
        if self.rate_limit_requests <= 0:
            issues.append(f"Rate limit requests must be positive: {self.rate_limit_requests}")
        if self.rate_limit_window <= 0:
            issues.append(f"Rate limit window must be positive: {self.rate_limit_window}")

        # Validate performance configuration
        if self.worker_processes <= 0:
            issues.append(f"Worker processes must be positive: {self.worker_processes}")
        if self.max_concurrent_analyses <= 0:
            issues.append(f"Max concurrent analyses must be positive: {self.max_concurrent_analyses}")
        if self.analysis_timeout <= 0:
            issues.append(f"Analysis timeout must be positive: {self.analysis_timeout}")
        if self.cleanup_interval <= 0:
            issues.append(f"Cleanup interval must be positive: {self.cleanup_interval}")

        return issues

    # GhidraMCP Configuration
    ghidra_mcp_host: str = Field(default="localhost", env="GHIDRA_AI_MCP_HOST")
    ghidra_mcp_port: int = Field(default=8080, env="GHIDRA_AI_MCP_PORT")
    ghidra_mcp_timeout: int = Field(default=30, env="GHIDRA_AI_MCP_TIMEOUT")
    ghidra_mcp_retry_attempts: int = Field(default=3, env="GHIDRA_AI_MCP_RETRY_ATTEMPTS")
    ghidra_mcp_enabled: bool = Field(default=True, env="GHIDRA_AI_MCP_ENABLED")

    # LLama.cpp Configuration
    llama_cpp_model_dir: str = Field(default="./llama-cpp/models", env="GHIDRA_AI_LLAMA_MODEL_DIR")
    llama_cpp_n_ctx: int = Field(default=4096, env="GHIDRA_AI_LLAMA_N_CTX")
    llama_cpp_n_threads: int = Field(default=-1, env="GHIDRA_AI_LLAMA_N_THREADS")
    llama_cpp_n_gpu_layers: int = Field(default=0, env="GHIDRA_AI_LLAMA_N_GPU_LAYERS")
    llama_cpp_use_mmap: bool = Field(default=True, env="GHIDRA_AI_LLAMA_USE_MMAP")
    llama_cpp_use_mlock: bool = Field(default=False, env="GHIDRA_AI_LLAMA_USE_MLOCK")
    llama_cpp_verbose: bool = Field(default=False, env="GHIDRA_AI_LLAMA_VERBOSE")

    # Frontend Configuration
    frontend_build_dir: str = Field(default="./frontend/build", env="GHIDRA_AI_FRONTEND_BUILD_DIR")
    frontend_dev_server_port: int = Field(default=3000, env="GHIDRA_AI_FRONTEND_DEV_PORT")
    frontend_api_base_url: str = Field(default="http://localhost:8000", env="GHIDRA_AI_FRONTEND_API_BASE_URL")

    # Testing Configuration
    test_data_dir: str = Field(default="./tests/data", env="GHIDRA_AI_TEST_DATA_DIR")
    test_temp_dir: str = Field(default="./tests/temp", env="GHIDRA_AI_TEST_TEMP_DIR")
    test_ghidra_projects_dir: str = Field(default="./tests/ghidra_projects", env="GHIDRA_AI_TEST_GHIDRA_PROJECTS_DIR")

    # Logging Configuration
    log_level: str = Field(default="INFO", env="GHIDRA_AI_LOG_LEVEL")
    log_file: Optional[str] = Field(default=None, env="GHIDRA_AI_LOG_FILE")
    log_structured: bool = Field(default=False, env="GHIDRA_AI_LOG_STRUCTURED")
    log_max_bytes: int = Field(default=10485760, env="GHIDRA_AI_LOG_MAX_BYTES")  # 10MB
    log_backup_count: int = Field(default=5, env="GHIDRA_AI_LOG_BACKUP_COUNT")

    # Security Configuration
    api_key: Optional[str] = Field(default=None, env="GHIDRA_AI_API_KEY")
    cors_origins: List[str] = Field(default=["http://localhost:3000"], env="GHIDRA_AI_CORS_ORIGINS")
    max_upload_size: int = Field(default=104857600, env="GHIDRA_AI_MAX_UPLOAD_SIZE")  # 100MB
    rate_limit_requests: int = Field(default=100, env="GHIDRA_AI_RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=3600, env="GHIDRA_AI_RATE_LIMIT_WINDOW")  # 1 hour

    # Performance Configuration
    worker_processes: int = Field(default=1, env="GHIDRA_AI_WORKER_PROCESSES")
    max_concurrent_analyses: int = Field(default=3, env="GHIDRA_AI_MAX_CONCURRENT_ANALYSES")
    analysis_timeout: int = Field(default=1800, env="GHIDRA_AI_ANALYSIS_TIMEOUT")  # 30 minutes
    cleanup_interval: int = Field(default=3600, env="GHIDRA_AI_CLEANUP_INTERVAL")  # 1 hour

    @property
    def ghidra_mcp_url(self) -> str:
        """Get full GhidraMCP URL."""
        return f"http://{self.ghidra_mcp_host}:{self.ghidra_mcp_port}"

    @property
    def llama_cpp_models_dir(self) -> Path:
        """Get LLama.cpp models directory as Path object."""
        return Path(self.llama_cpp_model_dir).resolve()

    @property
    def frontend_build_path(self) -> Path:
        """Get frontend build directory as Path object."""
        return Path(self.frontend_build_dir).resolve()

    @property
    def test_data_path(self) -> Path:
        """Get test data directory as Path object."""
        return Path(self.test_data_dir).resolve()

    @property
    def test_temp_path(self) -> Path:
        """Get test temp directory as Path object."""
        return Path(self.test_temp_dir).resolve()

    @property
    def log_file_path(self) -> Optional[Path]:
        """Get log file path as Path object if configured."""
        return Path(self.log_file).resolve() if self.log_file else None
