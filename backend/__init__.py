"""
Ghidra AI Agent Backend Package

This package provides the core backend functionality for the Ghidra AI Agent,
including AI agent orchestration, Ghidra integration, and API services.
"""

__version__ = "1.0.0"
__author__ = "Ghidra AI Agent Team"

# Import main components for easy access
from .core.agent import GhidraAIAgent
from .core.config import Settings
from .core.ghidra_interface import GhidraInterface, GhidraConnectionError

__all__ = [
    "GhidraAIAgent",
    "Settings", 
    "GhidraInterface",
    "GhidraConnectionError"
]
