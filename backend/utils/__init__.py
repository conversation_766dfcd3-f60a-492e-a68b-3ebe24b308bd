"""
Utility functions and helpers for Ghidra AI Agent.

This module contains shared utilities, helper functions,
and common functionality used across the application.
"""

# File operations
from .file_operations import (
    FileOperationError,
    FileValidator,
    TempFileManager,
    FileUtils,
    SUPPORTED_BINARY_TYPES,
    MAX_FILE_SIZE
)

# Validation utilities
from .validation import (
    ValidationError,
    CommonValidators,
    ConfigValidator,
    RequestValidator
)

# Logging utilities
from .logging_helpers import (
    StructuredFormatter,
    LoggerManager,
    LogContext,
    log_context,
    PerformanceLogger,
    SecurityLogger
)

# Exception classes
from .exceptions import (
    GhidraAIError,
    GhidraConnectionError,
    GhidraAnalysisError,
    FileOperationError as FileOpError,  # Avoid naming conflict
    ValidationError as ValidError,      # Avoid naming conflict
    ConfigurationError,
    ModelError,
    AuthenticationError,
    AuthorizationError,
    RateLimitError,
    ExternalServiceError,
    TimeoutError,
    create_http_exception,
    handle_exception
)

__all__ = [
    # File operations
    "FileOperationError",
    "FileValidator",
    "TempFileManager",
    "FileUtils",
    "SUPPORTED_BINARY_TYPES",
    "MAX_FILE_SIZE",

    # Validation
    "ValidationError",
    "CommonValidators",
    "ConfigValidator",
    "RequestValidator",

    # Logging
    "StructuredFormatter",
    "LoggerManager",
    "LogContext",
    "log_context",
    "PerformanceLogger",
    "SecurityLogger",

    # Exceptions
    "GhidraAIError",
    "GhidraConnectionError",
    "GhidraAnalysisError",
    "FileOpError",
    "ValidError",
    "ConfigurationError",
    "ModelError",
    "AuthenticationError",
    "AuthorizationError",
    "RateLimitError",
    "ExternalServiceError",
    "TimeoutError",
    "create_http_exception",
    "handle_exception"
]
