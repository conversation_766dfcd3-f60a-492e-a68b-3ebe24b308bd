"""
Logging utilities for Ghidra AI Agent.

This module provides structured logging utilities and helpers that integrate
with the existing logging patterns used throughout the application.
"""

import logging
import logging.handlers
import sys
import json
import traceback
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Union
from contextlib import contextmanager


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging with JSON output."""

    def __init__(self, include_extra: bool = True):
        """
        Initialize the structured formatter.

        Args:
            include_extra: Whether to include extra fields in log records
        """
        super().__init__()
        self.include_extra = include_extra

    def format(self, record: logging.LogRecord) -> str:
        """
        Format log record as structured JSON.

        Args:
            record: Log record to format

        Returns:
            Formatted log message as JSON string
        """
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }

        # Add exception information if present
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }

        # Add extra fields if enabled
        if self.include_extra:
            extra_fields = {
                key: value for key, value in record.__dict__.items()
                if key not in {
                    'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                    'filename', 'module', 'lineno', 'funcName', 'created',
                    'msecs', 'relativeCreated', 'thread', 'threadName',
                    'processName', 'process', 'getMessage', 'exc_info',
                    'exc_text', 'stack_info'
                }
            }
            if extra_fields:
                log_data["extra"] = extra_fields

        return json.dumps(log_data, default=str)


class LoggerManager:
    """Manager for application logging configuration."""

    @staticmethod
    def setup_logging(
        level: str = "INFO",
        log_file: Optional[Union[str, Path]] = None,
        structured: bool = False,
        max_bytes: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5
    ) -> None:
        """
        Setup application logging configuration.

        Args:
            level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_file: Optional log file path
            structured: Whether to use structured JSON logging
            max_bytes: Maximum log file size before rotation
            backup_count: Number of backup log files to keep
        """
        # Convert string level to logging constant
        numeric_level = getattr(logging, level.upper(), logging.INFO)

        # Create root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)

        # Clear existing handlers
        root_logger.handlers.clear()

        # Setup console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)

        if structured:
            console_formatter = StructuredFormatter()
        else:
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )

        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)

        # Setup file handler if log file is specified
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)

            file_handler = logging.handlers.RotatingFileHandler(
                log_path,
                maxBytes=max_bytes,
                backupCount=backup_count
            )
            file_handler.setLevel(numeric_level)

            if structured:
                file_formatter = StructuredFormatter()
            else:
                file_formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
                )

            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)

        # Set specific logger levels for third-party libraries
        logging.getLogger('aiohttp').setLevel(logging.WARNING)
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        logging.getLogger('asyncio').setLevel(logging.WARNING)

    @staticmethod
    def get_logger(name: str) -> logging.Logger:
        """
        Get a logger instance with the specified name.

        Args:
            name: Logger name (typically __name__)

        Returns:
            Logger instance
        """
        return logging.getLogger(name)


class LogContext:
    """Context manager for adding structured context to log messages."""

    def __init__(self, logger: logging.Logger, **context):
        """
        Initialize log context.

        Args:
            logger: Logger instance
            **context: Context fields to add to log messages
        """
        self.logger = logger
        self.context = context
        self.old_factory = None

    def __enter__(self):
        """Enter the context manager."""
        self.old_factory = logging.getLogRecordFactory()

        def record_factory(*args, **kwargs):
            record = self.old_factory(*args, **kwargs)
            for key, value in self.context.items():
                setattr(record, key, value)
            return record

        logging.setLogRecordFactory(record_factory)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the context manager."""
        if self.old_factory:
            logging.setLogRecordFactory(self.old_factory)


@contextmanager
def log_context(logger: logging.Logger, **context):
    """
    Context manager for adding structured context to log messages.

    Args:
        logger: Logger instance
        **context: Context fields to add to log messages

    Usage:
        with log_context(logger, request_id="123", user_id="456"):
            logger.info("Processing request")
    """
    with LogContext(logger, **context):
        yield


class PerformanceLogger:
    """Logger for performance monitoring and timing."""

    def __init__(self, logger: logging.Logger):
        """
        Initialize performance logger.

        Args:
            logger: Logger instance to use
        """
        self.logger = logger

    @contextmanager
    def time_operation(self, operation_name: str, **context):
        """
        Context manager for timing operations.

        Args:
            operation_name: Name of the operation being timed
            **context: Additional context to include in log messages

        Usage:
            with perf_logger.time_operation("binary_analysis", binary_path="/path/to/binary"):
                # Perform operation
                pass
        """
        import time

        start_time = time.time()

        with log_context(self.logger, operation=operation_name, **context):
            self.logger.info(f"Starting {operation_name}")

            try:
                yield

                duration = time.time() - start_time
                self.logger.info(
                    f"Completed {operation_name}",
                    extra={"duration_seconds": duration}
                )

            except Exception as e:
                duration = time.time() - start_time
                self.logger.error(
                    f"Failed {operation_name}: {e}",
                    extra={"duration_seconds": duration},
                    exc_info=True
                )
                raise


class SecurityLogger:
    """Logger for security-related events."""

    def __init__(self, logger: logging.Logger):
        """
        Initialize security logger.

        Args:
            logger: Logger instance to use
        """
        self.logger = logger

    def log_file_upload(self, filename: str, file_size: int, client_ip: str, user_agent: str = None):
        """
        Log file upload event.

        Args:
            filename: Name of uploaded file
            file_size: Size of uploaded file in bytes
            client_ip: Client IP address
            user_agent: Client user agent string
        """
        self.logger.info(
            "File upload",
            extra={
                "event_type": "file_upload",
                "filename": filename,
                "file_size": file_size,
                "client_ip": client_ip,
                "user_agent": user_agent
            }
        )

    def log_analysis_request(self, binary_path: str, analysis_type: str, client_ip: str):
        """
        Log binary analysis request.

        Args:
            binary_path: Path to binary being analyzed
            analysis_type: Type of analysis requested
            client_ip: Client IP address
        """
        self.logger.info(
            "Analysis request",
            extra={
                "event_type": "analysis_request",
                "binary_path": binary_path,
                "analysis_type": analysis_type,
                "client_ip": client_ip
            }
        )

    def log_authentication_attempt(self, success: bool, client_ip: str, reason: str = None):
        """
        Log authentication attempt.

        Args:
            success: Whether authentication was successful
            client_ip: Client IP address
            reason: Reason for failure (if applicable)
        """
        level = logging.INFO if success else logging.WARNING
        message = "Authentication successful" if success else f"Authentication failed: {reason}"

        self.logger.log(
            level,
            message,
            extra={
                "event_type": "authentication",
                "success": success,
                "client_ip": client_ip,
                "reason": reason
            }
        )