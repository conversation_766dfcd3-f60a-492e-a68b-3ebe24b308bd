"""
Validation utilities for Ghidra AI Agent.

This module provides common validation functions extending Pydantic patterns
and validation logic used throughout the application.
"""

import re
import logging
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
from pydantic import BaseModel, Field, validator
import ipaddress

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Custom exception for validation errors."""
    pass


class CommonValidators:
    """Common validation functions for various data types."""

    @staticmethod
    def validate_port(port: Union[int, str]) -> int:
        """
        Validate network port number.

        Args:
            port: Port number as int or string

        Returns:
            Valid port number as integer

        Raises:
            ValidationError: If port is invalid
        """
        try:
            port_int = int(port)
            if not (1 <= port_int <= 65535):
                raise ValidationError(f"Port must be between 1 and 65535, got: {port_int}")
            return port_int
        except (ValueError, TypeError):
            raise ValidationError(f"Invalid port format: {port}")

    @staticmethod
    def validate_host(host: str) -> str:
        """
        Validate hostname or IP address.

        Args:
            host: Hostname or IP address

        Returns:
            Valid host string

        Raises:
            ValidationError: If host is invalid
        """
        if not host or not isinstance(host, str):
            raise ValidationError("Host cannot be empty")

        host = host.strip()

        # Try to parse as IP address
        try:
            ipaddress.ip_address(host)
            return host
        except ValueError:
            pass

        # Validate as hostname
        if not re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$', host):
            raise ValidationError(f"Invalid hostname format: {host}")

        return host

    @staticmethod
    def validate_url(url: str) -> str:
        """
        Validate URL format.

        Args:
            url: URL string

        Returns:
            Valid URL string

        Raises:
            ValidationError: If URL is invalid
        """
        if not url or not isinstance(url, str):
            raise ValidationError("URL cannot be empty")

        url = url.strip()

        # Basic URL pattern validation
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)

        if not url_pattern.match(url):
            raise ValidationError(f"Invalid URL format: {url}")

        return url

    @staticmethod
    def validate_hex_address(address: str) -> str:
        """
        Validate hexadecimal address format.

        Args:
            address: Hexadecimal address string

        Returns:
            Valid hex address string

        Raises:
            ValidationError: If address is invalid
        """
        if not address or not isinstance(address, str):
            raise ValidationError("Address cannot be empty")

        address = address.strip()

        # Remove 0x prefix if present
        if address.lower().startswith('0x'):
            address = address[2:]

        # Validate hex format
        if not re.match(r'^[0-9a-fA-F]+$', address):
            raise ValidationError(f"Invalid hexadecimal address format: {address}")

        # Ensure reasonable length (4-16 hex digits)
        if not (4 <= len(address) <= 16):
            raise ValidationError(f"Address length must be between 4 and 16 hex digits: {address}")

        return f"0x{address.upper()}"

    @staticmethod
    def validate_analysis_type(analysis_type: str) -> str:
        """
        Validate analysis type parameter.

        Args:
            analysis_type: Type of analysis

        Returns:
            Valid analysis type

        Raises:
            ValidationError: If analysis type is invalid
        """
        valid_types = {'full', 'quick', 'security', 'function', 'custom'}

        if not analysis_type or not isinstance(analysis_type, str):
            raise ValidationError("Analysis type cannot be empty")

        analysis_type = analysis_type.lower().strip()

        if analysis_type not in valid_types:
            raise ValidationError(
                f"Invalid analysis type: {analysis_type}. "
                f"Valid types: {', '.join(valid_types)}"
            )

        return analysis_type


class ConfigValidator:
    """Validator for configuration settings."""

    @staticmethod
    def validate_model_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate LLM model configuration.

        Args:
            config: Model configuration dictionary

        Returns:
            Validated configuration

        Raises:
            ValidationError: If configuration is invalid
        """
        required_fields = {'model_name', 'max_tokens', 'temperature'}

        # Check required fields
        missing_fields = required_fields - set(config.keys())
        if missing_fields:
            raise ValidationError(f"Missing required model config fields: {missing_fields}")

        # Validate specific fields
        if not isinstance(config['model_name'], str) or not config['model_name'].strip():
            raise ValidationError("Model name must be a non-empty string")

        try:
            max_tokens = int(config['max_tokens'])
            if not (1 <= max_tokens <= 32768):
                raise ValidationError("max_tokens must be between 1 and 32768")
            config['max_tokens'] = max_tokens
        except (ValueError, TypeError):
            raise ValidationError("max_tokens must be a valid integer")

        try:
            temperature = float(config['temperature'])
            if not (0.0 <= temperature <= 2.0):
                raise ValidationError("temperature must be between 0.0 and 2.0")
            config['temperature'] = temperature
        except (ValueError, TypeError):
            raise ValidationError("temperature must be a valid float")

        return config

    @staticmethod
    def validate_ghidra_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate Ghidra configuration.

        Args:
            config: Ghidra configuration dictionary

        Returns:
            Validated configuration

        Raises:
            ValidationError: If configuration is invalid
        """
        # Validate host and port
        if 'ghidra_mcp_host' in config:
            config['ghidra_mcp_host'] = CommonValidators.validate_host(config['ghidra_mcp_host'])

        if 'ghidra_mcp_port' in config:
            config['ghidra_mcp_port'] = CommonValidators.validate_port(config['ghidra_mcp_port'])

        # Validate Ghidra path if provided
        if 'ghidra_path' in config and config['ghidra_path']:
            ghidra_path = Path(config['ghidra_path'])
            if not ghidra_path.exists():
                raise ValidationError(f"Ghidra path does not exist: {ghidra_path}")
            if not ghidra_path.is_dir():
                raise ValidationError(f"Ghidra path is not a directory: {ghidra_path}")

        # Validate projects directory
        if 'ghidra_projects_dir' in config:
            projects_dir = config['ghidra_projects_dir']
            if not isinstance(projects_dir, str) or not projects_dir.strip():
                raise ValidationError("Ghidra projects directory must be a non-empty string")

        return config


class RequestValidator:
    """Validator for API request data."""

    @staticmethod
    def validate_analysis_request(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate binary analysis request data.

        Args:
            data: Request data dictionary

        Returns:
            Validated request data

        Raises:
            ValidationError: If request data is invalid
        """
        # Validate binary path
        if 'binary_path' not in data:
            raise ValidationError("binary_path is required")

        binary_path = Path(data['binary_path'])
        if not binary_path.exists():
            raise ValidationError(f"Binary file not found: {binary_path}")

        if not binary_path.is_file():
            raise ValidationError(f"Binary path is not a file: {binary_path}")

        # Validate analysis type
        if 'analysis_type' in data:
            data['analysis_type'] = CommonValidators.validate_analysis_type(data['analysis_type'])

        # Validate project name if provided
        if 'project_name' in data and data['project_name']:
            project_name = data['project_name'].strip()
            if not re.match(r'^[a-zA-Z0-9_\-\.]+$', project_name):
                raise ValidationError("Project name can only contain letters, numbers, underscores, hyphens, and dots")
            data['project_name'] = project_name

        return data

    @staticmethod
    def validate_function_request(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate function analysis request data.

        Args:
            data: Request data dictionary

        Returns:
            Validated request data

        Raises:
            ValidationError: If request data is invalid
        """
        # Validate address
        if 'address' not in data:
            raise ValidationError("address is required")

        data['address'] = CommonValidators.validate_hex_address(data['address'])

        return data

    @staticmethod
    def validate_chat_request(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate chat request data.

        Args:
            data: Request data dictionary

        Returns:
            Validated request data

        Raises:
            ValidationError: If request data is invalid
        """
        # Validate message
        if 'message' not in data:
            raise ValidationError("message is required")

        message = data['message']
        if not isinstance(message, str) or not message.strip():
            raise ValidationError("Message must be a non-empty string")

        # Limit message length
        if len(message) > 10000:
            raise ValidationError("Message too long (max 10000 characters)")

        data['message'] = message.strip()

        # Validate context if provided
        if 'context' in data and data['context'] is not None:
            if not isinstance(data['context'], dict):
                raise ValidationError("Context must be a dictionary")

        return data