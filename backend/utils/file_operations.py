"""
File operations utilities for Ghidra AI Agent.

This module provides safe file handling, binary validation, and temporary
directory management utilities extending patterns from the existing codebase.
"""

import asyncio
import hashlib
import logging
import mimetypes
import os
import shutil
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple, Union

logger = logging.getLogger(__name__)

# Supported binary file types for analysis
SUPPORTED_BINARY_TYPES = {
    'application/x-executable',
    'application/x-sharedlib',
    'application/x-object',
    'application/x-dosexec',
    'application/x-mach-binary',
    'application/octet-stream'
}

# Maximum file size for uploads (100MB)
MAX_FILE_SIZE = 100 * 1024 * 1024

# Temporary directory for file operations
TEMP_DIR_NAME = "ghidra_ai_agent"


class FileOperationError(Exception):
    """Custom exception for file operation errors."""
    pass


class FileValidator:
    """Validator for binary files and uploads."""

    @staticmethod
    def validate_file_size(file_path: Union[str, Path], max_size: int = MAX_FILE_SIZE) -> bool:
        """
        Validate file size is within limits.

        Args:
            file_path: Path to the file
            max_size: Maximum allowed file size in bytes

        Returns:
            True if file size is valid

        Raises:
            FileOperationError: If file is too large or doesn't exist
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileOperationError(f"File not found: {file_path}")

            file_size = file_path.stat().st_size
            if file_size > max_size:
                raise FileOperationError(
                    f"File too large: {file_size} bytes (max: {max_size} bytes)"
                )

            return True
        except OSError as e:
            raise FileOperationError(f"Failed to check file size: {e}")

    @staticmethod
    def validate_binary_type(file_path: Union[str, Path]) -> bool:
        """
        Validate that file is a supported binary type.

        Args:
            file_path: Path to the file

        Returns:
            True if file type is supported

        Raises:
            FileOperationError: If file type is not supported
        """
        try:
            file_path = Path(file_path)

            # Use mimetypes for basic file type detection
            mime_type, _ = mimetypes.guess_type(str(file_path))

            # Fallback to checking file extensions for binary files
            if file_path.suffix.lower() in {'.exe', '.dll', '.so', '.dylib', '.bin', '.elf'}:
                logger.info(f"Accepting binary file based on extension: {file_path.suffix}")
                return True

            if mime_type and mime_type not in SUPPORTED_BINARY_TYPES:
                raise FileOperationError(
                    f"Unsupported file type: {mime_type}. "
                    f"Supported types: {', '.join(SUPPORTED_BINARY_TYPES)}"
                )

            return True
        except Exception as e:
            if isinstance(e, FileOperationError):
                raise
            raise FileOperationError(f"Failed to validate file type: {e}")

    @staticmethod
    def validate_filename(filename: str) -> bool:
        """
        Validate filename for security and compatibility.

        Args:
            filename: Name of the file

        Returns:
            True if filename is valid

        Raises:
            FileOperationError: If filename is invalid
        """
        if not filename or filename.strip() == "":
            raise FileOperationError("Filename cannot be empty")

        # Check for dangerous characters
        dangerous_chars = {'/', '\\', '..', '<', '>', ':', '"', '|', '?', '*'}
        if any(char in filename for char in dangerous_chars):
            raise FileOperationError(f"Filename contains invalid characters: {filename}")

        # Check filename length
        if len(filename) > 255:
            raise FileOperationError("Filename too long (max 255 characters)")

        return True


class TempFileManager:
    """Manager for temporary file operations."""

    @staticmethod
    def get_temp_dir() -> Path:
        """
        Get or create the application temporary directory.

        Returns:
            Path to the temporary directory
        """
        temp_dir = Path(tempfile.gettempdir()) / TEMP_DIR_NAME
        temp_dir.mkdir(exist_ok=True)
        return temp_dir

    @staticmethod
    async def save_upload_file(upload_file, filename: Optional[str] = None) -> Tuple[Path, Dict[str, Any]]:
        """
        Save uploaded file to temporary directory with validation.

        Args:
            upload_file: FastAPI UploadFile object
            filename: Optional custom filename

        Returns:
            Tuple of (file_path, file_info)

        Raises:
            FileOperationError: If file operations fail
        """
        try:
            # Use provided filename or original filename
            final_filename = filename or upload_file.filename
            if not final_filename:
                raise FileOperationError("No filename provided")

            # Validate filename
            FileValidator.validate_filename(final_filename)

            # Get temporary directory
            temp_dir = TempFileManager.get_temp_dir()
            file_path = temp_dir / final_filename

            # Read file content
            content = await upload_file.read()

            # Validate file size
            if len(content) > MAX_FILE_SIZE:
                raise FileOperationError(
                    f"File too large: {len(content)} bytes (max: {MAX_FILE_SIZE} bytes)"
                )

            # Save file
            with open(file_path, 'wb') as f:
                f.write(content)

            # Validate binary type
            FileValidator.validate_binary_type(file_path)

            # Calculate file hash
            file_hash = hashlib.sha256(content).hexdigest()

            # Gather file information
            file_info = {
                "filename": final_filename,
                "path": str(file_path),
                "size": len(content),
                "hash": file_hash,
                "mime_type": mimetypes.guess_type(str(file_path))[0] or "application/octet-stream"
            }

            logger.info(f"File saved successfully: {file_path} ({len(content)} bytes)")
            return file_path, file_info

        except Exception as e:
            if isinstance(e, FileOperationError):
                raise
            raise FileOperationError(f"Failed to save upload file: {e}")

    @staticmethod
    def cleanup_temp_files(max_age_hours: int = 24) -> int:
        """
        Clean up old temporary files.

        Args:
            max_age_hours: Maximum age of files to keep in hours

        Returns:
            Number of files cleaned up
        """
        try:
            temp_dir = TempFileManager.get_temp_dir()
            if not temp_dir.exists():
                return 0

            import time
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            cleaned_count = 0

            for file_path in temp_dir.iterdir():
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        try:
                            file_path.unlink()
                            cleaned_count += 1
                            logger.debug(f"Cleaned up old temp file: {file_path}")
                        except OSError as e:
                            logger.warning(f"Failed to delete temp file {file_path}: {e}")

            logger.info(f"Cleaned up {cleaned_count} temporary files")
            return cleaned_count

        except Exception as e:
            logger.error(f"Failed to cleanup temp files: {e}")
            return 0


class FileUtils:
    """General file utility functions."""

    @staticmethod
    async def copy_file_async(src: Union[str, Path], dst: Union[str, Path]) -> None:
        """
        Asynchronously copy a file.

        Args:
            src: Source file path
            dst: Destination file path

        Raises:
            FileOperationError: If copy operation fails
        """
        try:
            src_path = Path(src)
            dst_path = Path(dst)

            if not src_path.exists():
                raise FileOperationError(f"Source file not found: {src_path}")

            # Create destination directory if needed
            dst_path.parent.mkdir(parents=True, exist_ok=True)

            # Copy file in executor to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, shutil.copy2, str(src_path), str(dst_path))

            logger.debug(f"File copied: {src_path} -> {dst_path}")

        except Exception as e:
            if isinstance(e, FileOperationError):
                raise
            raise FileOperationError(f"Failed to copy file: {e}")

    @staticmethod
    def ensure_directory(directory: Union[str, Path]) -> Path:
        """
        Ensure directory exists, create if necessary.

        Args:
            directory: Directory path

        Returns:
            Path object for the directory

        Raises:
            FileOperationError: If directory creation fails
        """
        try:
            dir_path = Path(directory)
            dir_path.mkdir(parents=True, exist_ok=True)
            return dir_path
        except Exception as e:
            raise FileOperationError(f"Failed to create directory {directory}: {e}")

    @staticmethod
    def get_file_info(file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Get comprehensive file information.

        Args:
            file_path: Path to the file

        Returns:
            Dictionary with file information

        Raises:
            FileOperationError: If file doesn't exist or can't be accessed
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileOperationError(f"File not found: {file_path}")

            stat = file_path.stat()

            # Calculate file hash
            with open(file_path, 'rb') as f:
                file_hash = hashlib.sha256(f.read()).hexdigest()

            return {
                "name": file_path.name,
                "path": str(file_path.absolute()),
                "size": stat.st_size,
                "modified": stat.st_mtime,
                "created": stat.st_ctime,
                "hash": file_hash,
                "mime_type": mimetypes.guess_type(str(file_path))[0] or "application/octet-stream",
                "is_executable": os.access(file_path, os.X_OK)
            }

        except Exception as e:
            if isinstance(e, FileOperationError):
                raise
            raise FileOperationError(f"Failed to get file info: {e}")

    @staticmethod
    def safe_filename(filename: str) -> str:
        """
        Create a safe filename by removing/replacing dangerous characters.

        Args:
            filename: Original filename

        Returns:
            Safe filename
        """
        # Replace dangerous characters with underscores
        safe_chars = []
        for char in filename:
            if char in {'/', '\\', '<', '>', ':', '"', '|', '?', '*'}:
                safe_chars.append('_')
            elif char == '..':
                safe_chars.append('_')
            else:
                safe_chars.append(char)

        safe_name = ''.join(safe_chars).strip()

        # Ensure filename is not empty and not too long
        if not safe_name:
            safe_name = "unnamed_file"

        if len(safe_name) > 255:
            name_part, ext_part = os.path.splitext(safe_name)
            max_name_len = 255 - len(ext_part)
            safe_name = name_part[:max_name_len] + ext_part

        return safe_name