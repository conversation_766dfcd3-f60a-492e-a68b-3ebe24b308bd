"""
Custom exception classes for Ghidra AI Agent.

This module defines custom exception hierarchy extending the existing
GhidraConnectionError pattern used throughout the application.
"""

from typing import Optional, Dict, Any


class GhidraAIError(Exception):
    """Base exception class for Ghidra AI Agent errors."""

    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        """
        Initialize the base exception.

        Args:
            message: Error message
            error_code: Optional error code for categorization
            details: Optional additional error details
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert exception to dictionary for API responses.

        Returns:
            Dictionary representation of the exception
        """
        return {
            "error": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details
        }


class GhidraConnectionError(GhidraAIError):
    """Exception raised when connection to Ghidra fails."""

    def __init__(self, message: str, host: Optional[str] = None, port: Optional[int] = None):
        """
        Initialize Ghidra connection error.

        Args:
            message: Error message
            host: Ghidra host that failed to connect
            port: Ghidra port that failed to connect
        """
        details = {}
        if host:
            details["host"] = host
        if port:
            details["port"] = port

        super().__init__(message, "GHIDRA_CONNECTION_ERROR", details)


class GhidraAnalysisError(GhidraAIError):
    """Exception raised when Ghidra analysis fails."""

    def __init__(self, message: str, binary_path: Optional[str] = None, analysis_type: Optional[str] = None):
        """
        Initialize Ghidra analysis error.

        Args:
            message: Error message
            binary_path: Path to binary that failed analysis
            analysis_type: Type of analysis that failed
        """
        details = {}
        if binary_path:
            details["binary_path"] = binary_path
        if analysis_type:
            details["analysis_type"] = analysis_type

        super().__init__(message, "GHIDRA_ANALYSIS_ERROR", details)


class FileOperationError(GhidraAIError):
    """Exception raised when file operations fail."""

    def __init__(self, message: str, file_path: Optional[str] = None, operation: Optional[str] = None):
        """
        Initialize file operation error.

        Args:
            message: Error message
            file_path: Path to file that caused the error
            operation: Type of operation that failed
        """
        details = {}
        if file_path:
            details["file_path"] = file_path
        if operation:
            details["operation"] = operation

        super().__init__(message, "FILE_OPERATION_ERROR", details)


class ValidationError(GhidraAIError):
    """Exception raised when validation fails."""

    def __init__(self, message: str, field: Optional[str] = None, value: Optional[Any] = None):
        """
        Initialize validation error.

        Args:
            message: Error message
            field: Field that failed validation
            value: Value that failed validation
        """
        details = {}
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = str(value)

        super().__init__(message, "VALIDATION_ERROR", details)


class ConfigurationError(GhidraAIError):
    """Exception raised when configuration is invalid."""

    def __init__(self, message: str, config_key: Optional[str] = None):
        """
        Initialize configuration error.

        Args:
            message: Error message
            config_key: Configuration key that is invalid
        """
        details = {}
        if config_key:
            details["config_key"] = config_key

        super().__init__(message, "CONFIGURATION_ERROR", details)


class ModelError(GhidraAIError):
    """Exception raised when LLM model operations fail."""

    def __init__(self, message: str, model_name: Optional[str] = None, operation: Optional[str] = None):
        """
        Initialize model error.

        Args:
            message: Error message
            model_name: Name of the model that failed
            operation: Type of operation that failed
        """
        details = {}
        if model_name:
            details["model_name"] = model_name
        if operation:
            details["operation"] = operation

        super().__init__(message, "MODEL_ERROR", details)


class AuthenticationError(GhidraAIError):
    """Exception raised when authentication fails."""

    def __init__(self, message: str, auth_type: Optional[str] = None):
        """
        Initialize authentication error.

        Args:
            message: Error message
            auth_type: Type of authentication that failed
        """
        details = {}
        if auth_type:
            details["auth_type"] = auth_type

        super().__init__(message, "AUTHENTICATION_ERROR", details)


class AuthorizationError(GhidraAIError):
    """Exception raised when authorization fails."""

    def __init__(self, message: str, resource: Optional[str] = None, action: Optional[str] = None):
        """
        Initialize authorization error.

        Args:
            message: Error message
            resource: Resource that access was denied to
            action: Action that was denied
        """
        details = {}
        if resource:
            details["resource"] = resource
        if action:
            details["action"] = action

        super().__init__(message, "AUTHORIZATION_ERROR", details)


class RateLimitError(GhidraAIError):
    """Exception raised when rate limits are exceeded."""

    def __init__(self, message: str, limit: Optional[int] = None, window: Optional[str] = None):
        """
        Initialize rate limit error.

        Args:
            message: Error message
            limit: Rate limit that was exceeded
            window: Time window for the rate limit
        """
        details = {}
        if limit:
            details["limit"] = limit
        if window:
            details["window"] = window

        super().__init__(message, "RATE_LIMIT_ERROR", details)


class ExternalServiceError(GhidraAIError):
    """Exception raised when external service calls fail."""

    def __init__(self, message: str, service: Optional[str] = None, status_code: Optional[int] = None):
        """
        Initialize external service error.

        Args:
            message: Error message
            service: Name of the external service
            status_code: HTTP status code if applicable
        """
        details = {}
        if service:
            details["service"] = service
        if status_code:
            details["status_code"] = status_code

        super().__init__(message, "EXTERNAL_SERVICE_ERROR", details)


class TimeoutError(GhidraAIError):
    """Exception raised when operations timeout."""

    def __init__(self, message: str, timeout_seconds: Optional[float] = None, operation: Optional[str] = None):
        """
        Initialize timeout error.

        Args:
            message: Error message
            timeout_seconds: Timeout value that was exceeded
            operation: Operation that timed out
        """
        details = {}
        if timeout_seconds:
            details["timeout_seconds"] = timeout_seconds
        if operation:
            details["operation"] = operation

        super().__init__(message, "TIMEOUT_ERROR", details)


# Exception mapping for HTTP status codes
HTTP_EXCEPTION_MAP = {
    400: ValidationError,
    401: AuthenticationError,
    403: AuthorizationError,
    404: FileOperationError,
    408: TimeoutError,
    429: RateLimitError,
    500: GhidraAIError,
    502: ExternalServiceError,
    503: ExternalServiceError,
    504: TimeoutError
}


def create_http_exception(status_code: int, message: str, **kwargs) -> GhidraAIError:
    """
    Create appropriate exception based on HTTP status code.

    Args:
        status_code: HTTP status code
        message: Error message
        **kwargs: Additional exception parameters

    Returns:
        Appropriate exception instance
    """
    exception_class = HTTP_EXCEPTION_MAP.get(status_code, GhidraAIError)
    return exception_class(message, **kwargs)


def handle_exception(func):
    """
    Decorator to handle exceptions and convert them to appropriate HTTP responses.

    Args:
        func: Function to decorate

    Returns:
        Decorated function
    """
    import functools
    from fastapi import HTTPException

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except GhidraAIError as e:
            # Convert custom exceptions to HTTP exceptions
            status_code = 500
            if isinstance(e, ValidationError):
                status_code = 400
            elif isinstance(e, AuthenticationError):
                status_code = 401
            elif isinstance(e, AuthorizationError):
                status_code = 403
            elif isinstance(e, FileOperationError):
                status_code = 404
            elif isinstance(e, RateLimitError):
                status_code = 429
            elif isinstance(e, TimeoutError):
                status_code = 408
            elif isinstance(e, ExternalServiceError):
                status_code = 502

            raise HTTPException(status_code=status_code, detail=e.to_dict())
        except Exception as e:
            # Handle unexpected exceptions
            error = GhidraAIError(f"Unexpected error: {str(e)}")
            raise HTTPException(status_code=500, detail=error.to_dict())

    return wrapper