# Testing Environment Configuration
# This file contains testing-specific settings for Ghidra AI Agent

# Server Settings
server:
  host: "127.0.0.1"
  port: 8001  # Different port for testing
  debug: true
  serve_frontend: false  # Don't serve frontend during tests
  reload: false

# AI Model Settings
model:
  path: "./tests/models"
  name: "test-model.gguf"  # Smaller test model
  max_tokens: 512  # Smaller for faster tests
  temperature: 0.1  # Lower temperature for consistent test results

# LLama.cpp Settings
llama_cpp:
  model_dir: "./tests/models"
  n_ctx: 1024  # Smaller context for tests
  n_threads: 1
  n_gpu_layers: 0  # No GPU for tests
  use_mmap: false
  use_mlock: false
  verbose: false

# GhidraMCP Settings
ghidra_mcp:
  host: "localhost"
  port: 8081  # Different port for testing
  timeout: 10  # Shorter timeout for tests
  retry_attempts: 1
  enabled: false  # Disable MCP for unit tests

# Ghidra Settings
ghidra:
  path: ""  # No real Ghidra path needed for tests
  projects_dir: "./tests/temp/ghidra_projects"

# Frontend Settings
frontend:
  build_dir: "./tests/temp/frontend"
  dev_server_port: 3001
  api_base_url: "http://localhost:8001"

# Testing Settings
testing:
  data_dir: "./tests/data"
  temp_dir: "./tests/temp"
  ghidra_projects_dir: "./tests/temp/ghidra_projects"

# Logging Settings
logging:
  level: "DEBUG"  # Verbose logging for debugging tests
  file: "./tests/temp/test.log"
  structured: false
  max_bytes: 1048576  # 1MB
  backup_count: 1

# Security Settings
security:
  api_key: "test-api-key"  # Fixed key for tests
  cors_origins:
    - "http://localhost:3001"
  max_upload_size: 1048576  # 1MB for tests
  rate_limit_requests: 10000  # High limit for tests
  rate_limit_window: 60

# Performance Settings
performance:
  worker_processes: 1
  max_concurrent_analyses: 1  # Single analysis for tests
  analysis_timeout: 60  # Short timeout for tests
  cleanup_interval: 30  # Frequent cleanup during tests