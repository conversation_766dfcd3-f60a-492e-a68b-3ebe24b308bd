# Production Environment Configuration
# This file contains production-specific settings for Ghidra AI Agent

# Server Settings
server:
  host: "0.0.0.0"
  port: 8000
  debug: false
  serve_frontend: true
  reload: false

# AI Model Settings
model:
  path: "/opt/ghidra-ai/models"
  name: "llama-2-7b-chat.gguf"
  max_tokens: 2048
  temperature: 0.7

# LLama.cpp Settings
llama_cpp:
  model_dir: "/opt/ghidra-ai/llama-cpp/models"
  n_ctx: 4096
  n_threads: -1
  n_gpu_layers: 32  # Use GPU acceleration in production
  use_mmap: true
  use_mlock: true  # Lock memory in production
  verbose: false

# GhidraMCP Settings
ghidra_mcp:
  host: "ghidra-mcp"  # Docker service name or production host
  port: 8080
  timeout: 60  # Longer timeout for production
  retry_attempts: 5
  enabled: true

# Ghidra Settings
ghidra:
  path: "/opt/ghidra"
  projects_dir: "/data/ghidra_projects"

# Frontend Settings
frontend:
  build_dir: "/opt/ghidra-ai/frontend/build"
  dev_server_port: 3000
  api_base_url: "https://api.ghidra-ai.example.com"

# Testing Settings
testing:
  data_dir: "/opt/ghidra-ai/tests/data"
  temp_dir: "/tmp/ghidra-ai-tests"
  ghidra_projects_dir: "/tmp/test_ghidra_projects"

# Logging Settings
logging:
  level: "INFO"
  file: "/var/log/ghidra-ai/ghidra-ai.log"
  structured: true  # Use structured logging in production
  max_bytes: 52428800  # 50MB
  backup_count: 10

# Security Settings
security:
  api_key: "${GHIDRA_AI_API_KEY}"  # Required in production
  cors_origins:
    - "https://ghidra-ai.example.com"
    - "https://app.ghidra-ai.example.com"
  max_upload_size: 104857600
  rate_limit_requests: 100
  rate_limit_window: 3600

# Performance Settings
performance:
  worker_processes: 4  # Multiple workers for production
  max_concurrent_analyses: 5
  analysis_timeout: 3600  # Longer timeout for complex analyses
  cleanup_interval: 3600