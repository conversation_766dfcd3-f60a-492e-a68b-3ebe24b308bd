# Development Environment Configuration
# This file contains development-specific settings for Ghidra AI Agent

# Server Settings
server:
  host: "127.0.0.1"
  port: 8000
  debug: true
  serve_frontend: true
  reload: true

# AI Model Settings
model:
  path: "./models"
  name: "llama-2-7b-chat.gguf"
  max_tokens: 2048
  temperature: 0.7

# LLama.cpp Settings
llama_cpp:
  model_dir: "./llama-cpp/models"
  n_ctx: 4096
  n_threads: -1
  n_gpu_layers: 0
  use_mmap: true
  use_mlock: false
  verbose: true  # Enable verbose logging in development

# GhidraMCP Settings
ghidra_mcp:
  host: "localhost"
  port: 8080
  timeout: 30
  retry_attempts: 3
  enabled: true

# Ghidra Settings
ghidra:
  path: ""
  projects_dir: "./dev_ghidra_projects"

# Frontend Settings
frontend:
  build_dir: "./frontend/build"
  dev_server_port: 3000
  api_base_url: "http://localhost:8000"

# Testing Settings
testing:
  data_dir: "./tests/data"
  temp_dir: "./tests/temp"
  ghidra_projects_dir: "./tests/ghidra_projects"

# Logging Settings
logging:
  level: "DEBUG"  # More verbose logging in development
  file: "./logs/ghidra-ai-dev.log"
  structured: false
  max_bytes: 10485760
  backup_count: 3

# Security Settings
security:
  api_key: null  # No API key required in development
  cors_origins:
    - "http://localhost:3000"
    - "http://127.0.0.1:3000"
  max_upload_size: 104857600
  rate_limit_requests: 1000  # Higher limit for development
  rate_limit_window: 3600

# Performance Settings
performance:
  worker_processes: 1
  max_concurrent_analyses: 2  # Lower for development
  analysis_timeout: 1800
  cleanup_interval: 1800  # More frequent cleanup in development